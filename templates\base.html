{% load static %}

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'img/favicon.ico' %}"
    />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
      integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Google Fonts - Enhanced Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Main CSS -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />

    <style>
      :root {
        --primary-color: #7ab2d3;
        --primary-dark: #5a9bd4;
        --primary-light: #a8c8e1;
        --secondary-color: #f8fafc;
        --accent-color: #667eea;
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1),
          0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --gradient-primary: linear-gradient(
          135deg,
          var(--primary-color) 0%,
          var(--primary-dark) 100%
        );
        --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        --border-radius-sm: 0.375rem;
        --border-radius-md: 0.5rem;
        --border-radius-lg: 0.75rem;
        --border-radius-xl: 1rem;
        --transition-fast: 150ms ease-in-out;
        --transition-normal: 250ms ease-in-out;
        --transition-slow: 350ms ease-in-out;
      }

      * {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      .font-display {
        font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
      }

      /* Smooth scrolling and modern animations */
      html {
        scroll-behavior: smooth;
      }

      /* Enhanced focus styles */
      *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }

      /* Modern glassmorphism effect */
      .glass-effect {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Enhanced button styles */
      .btn-modern {
        position: relative;
        overflow: hidden;
        transition: all var(--transition-normal);
        transform: translateY(0);
      }

      .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn-modern:active {
        transform: translateY(0);
      }

      /* Modern card styles */
      .card-modern {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border-color);
        transition: all var(--transition-normal);
      }

      .card-modern:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
      }

      /* Loading animation */
      .loading-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      /* Print styles */
      @media print {
        body * {
          display: none;
        }

        .print-section {
          display: flex;
        }
      }

      /* Dark mode support preparation */
      @media (prefers-color-scheme: dark) {
        :root {
          --text-primary: #f7fafc;
          --text-secondary: #e2e8f0;
          --text-muted: #a0aec0;
          --secondary-color: #2d3748;
          --border-color: #4a5568;
        }
      }
    </style>
    {% block style %}{% endblock %}
    <title>{% block title %} {% endblock %} Tiny Feet MIS</title>
  </head>
  <body
    class="flex flex-col text-[var(--text-primary)] justify-center items-center w-full min-h-screen bg-gradient-to-br from-slate-50 to-blue-50"
  >
    <header
      class="w-full bg-white/95 backdrop-blur-md shadow-lg border-b border-white/20 sticky top-0 z-50"
    >
      <nav
        class="flex flex-row justify-between items-center w-full px-6 py-4 relative max-w-7xl mx-auto"
      >
        <section class="flex items-center gap-4">
          <div class="relative">
            <img
              src="{% static 'img/tinyfeet.jpg' %}"
              alt="Tiny Feet Academy Logo"
              width="42"
              height="42"
              class="rounded-xl shadow-md ring-2 ring-white/50 transition-transform duration-300 hover:scale-105"
            />
            <div
              class="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white animate-pulse"
            ></div>
          </div>
          <div class="flex flex-col">
            <h1
              class="font-display font-bold text-base tracking-tight text-gray-800 text-no-wrap"
            >
              Tiny Feet <span class="text-[var(--primary-color)]">Academy</span>
            </h1>
            <p class="text-xs text-gray-500 font-medium text-no-wrap">
              Management Information System
            </p>
          </div>
        </section>

        <!-- Mobile menu button -->
        <button
          id="mobile-menu-button"
          class="md:hidden flex items-center justify-center w-11 h-11 text-gray-600 hover:text-[var(--primary-color)] focus:outline-none rounded-xl hover:bg-gray-100/80 transition-all duration-300 btn-modern"
          aria-label="Toggle mobile menu"
          onclick="toggleMobileMenu()"
        >
          <svg
            class="w-6 h-6 transition-transform duration-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2.5"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>

        <!-- Desktop navigation -->
        <section
          class="hidden md:flex items-center gap-8 justify-center w-full"
        >
          <ul
            class="flex items-center gap-4 text-gray-600 font-medium text-sm justify-center w-full"
          >
            <li>
              <a
                href="{% url 'students:home' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'home' %}nav-active{% endif %} group"
              >
                <i class="fas fa-chart-line mr-2 text-sm"></i>Dashboard
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            <li>
              <a
                href="{% url 'students:classes' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'classes' %}nav-active{% endif %} group"
              >
                <i class="fas fa-users mr-2 text-sm"></i>Classes
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            <li>
              <a
                href="{% url 'finances:receipts' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'receipts' %}nav-active{% endif %} group"
              >
                <i class="fas fa-receipt mr-2 text-sm"></i>Receipts
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            <li>
              <a
                href="{% url 'finances:expenditures' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'expenditures' %}nav-active{% endif %} group"
              >
                <i class="fas fa-wallet mr-2 text-sm"></i>Finances
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            {% load rbac_tags %}
            {% if user|has_permission:"manage_system" or user.is_admin %}
            <li>
              <a
                href="{% url 'accounts:user_management' %}"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if 'user_management' in request.resolver_match.url_name %}nav-active{% endif %} group"
              >
                <i class="fas fa-users-cog mr-2 text-sm"></i>Users
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </a>
            </li>
            {% endif %}
            <li class="relative group">
              <button
                id="report"
                type="button"
                class="relative px-3 py-2 rounded-lg hover:text-[var(--primary-color)] transition-all duration-300 font-semibold {% if request.resolver_match.url_name == 'reports' %}nav-active{% endif %} flex items-center gap-2 focus:outline-none group"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i class="fas fa-chart-bar mr-2 text-sm"></i>Reports
                <svg
                  class="w-4 h-4 text-gray-400 group-hover:text-[var(--primary-color)] transition-all duration-300 transform group-hover:rotate-180"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2.5"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
                <span
                  class="absolute inset-x-0 -bottom-1 h-0.5 bg-[var(--primary-color)] transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"
                ></span>
              </button>
              <div class="hover-buffer"></div>
              <section
                id="flyout"
                class="opacity-0 scale-95 pointer-events-none invisible absolute left-1/2 -translate-x-1/2 top-full mt-4 min-w-[280px] bg-white/95 backdrop-blur-md shadow-2xl rounded-2xl flex-col items-start py-4 px-3 transition-all duration-300 ease-out group-hover:opacity-100 group-hover:scale-100 group-hover:pointer-events-auto group-hover:visible focus-within:opacity-100 focus-within:scale-100 focus-within:pointer-events-auto focus-within:visible z-50 border border-gray-200/50"
                tabindex="-1"
              >
                <div id="report-list" class="flex flex-col gap-2 w-full">
                  <div class="px-4 py-2 border-b border-gray-100">
                    <h3
                      class="text-xs font-semibold text-gray-500 uppercase tracking-wider"
                    >
                      Financial Reports
                    </h3>
                  </div>
                  <a
                    href="{% url 'finances:income_statement' %}"
                    class="flex items-center w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group"
                  >
                    <i
                      class="fas fa-chart-line mr-3 text-green-500 group-hover:scale-110 transition-transform"
                    ></i>
                    <div class="flex flex-col">
                      <span>Income Statement</span>
                      <span class="text-xs text-gray-500"
                        >Revenue analysis</span
                      >
                    </div>
                  </a>
                  <a
                    href="{% url 'finances:expenditure_statement' %}"
                    class="flex items-center w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group"
                  >
                    <i
                      class="fas fa-chart-pie mr-3 text-red-500 group-hover:scale-110 transition-transform"
                    ></i>
                    <div class="flex flex-col">
                      <span>Expenditure Statement</span>
                      <span class="text-xs text-gray-500"
                        >Expense breakdown</span
                      >
                    </div>
                  </a>
                  <a
                    href="{% url 'finances:fee_reports' %}"
                    class="flex items-center w-full px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 hover:text-[var(--primary-color)] transition-all duration-300 font-medium group"
                  >
                    <i
                      class="fas fa-money-check-alt mr-3 text-purple-500 group-hover:scale-110 transition-transform"
                    ></i>
                    <div class="flex flex-col">
                      <span>Fee Payment Report</span>
                      <span class="text-xs text-gray-500"
                        >Payment tracking</span
                      >
                    </div>
                  </a>
                </div>
              </section>
            </li>
          </ul>
          <div class="relative group flex items-center gap-3 justify-center">
            <div
              class="flex items-center gap-3 cursor-pointer group px-3 py-2 rounded-xl hover:bg-gray-50/80 transition-all duration-300"
              tabindex="0"
            >
              <div class="relative">
                <img
                  src="{% static 'img/person.png' %}"
                  alt="Profile"
                  class="rounded-full bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-white shadow-lg ring-2 ring-gray-100"
                  width="36"
                  height="36"
                />
                <div
                  class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"
                ></div>
              </div>
              <div class="flex flex-col">
                <span class="text-sm font-semibold capitalize text-gray-800"
                  >{{ request.user }}</span
                >
                <span class="text-xs text-gray-500">
                  {% if request.user.is_superuser %}
                  <!-- user role -->
                  Super Admin {% else%}
                  <!-- user role -->
                  Administrator {% endif %}
                </span>
              </div>
              <i
                class="fa-solid fa-chevron-down text-xs text-gray-400 group-hover:text-[var(--primary-color)] transition-all duration-300 transform group-hover:rotate-180"
              ></i>
            </div>
            <div class="hover-buffer"></div>
            <div
              class="opacity-0 pointer-events-none invisible absolute right-0 top-full min-w-64 bg-white/95 backdrop-blur-md shadow-2xl rounded-2xl flex flex-col items-start py-3 px-2 transition-all duration-300 group-hover:opacity-100 group-hover:pointer-events-auto group-hover:visible group-focus-within:opacity-100 group-focus-within:pointer-events-auto group-focus-within:visible hover:opacity-100 hover:pointer-events-auto hover:visible z-50 mt-3 border border-gray-200/50"
              tabindex="-1"
            >
              <div class="px-4 py-3 border-b border-gray-100 w-full">
                <div class="flex items-center gap-3 w-full">
                  <img
                    src="{% static 'img/person.png' %}"
                    alt="Profile"
                    class="rounded-full bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-white shadow-md"
                    width="32"
                    height="32"
                  />
                  <div class="flex flex-col">
                    <span class="text-sm font-semibold capitalize text-gray-800"
                      >{{ request.user }}</span
                    >
                    <span class="text-xs text-gray-500">
                      {% if request.user.is_superuser %}
                      <!-- user role -->
                      Super Administrator{% else %}
                      <!-- user role -->
                      Administrator{% endif %}
                    </span>
                  </div>
                </div>
              </div>

              <div class="px-2 py-2 w-full">
                <a
                  href="{% url 'accounts:user_profile' %}"
                  class="flex items-center gap-3 w-full text-gray-700 hover:text-[var(--primary-color)] px-3 py-2 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 font-medium group"
                  title="My Account"
                >
                  <i
                    class="fa-solid fa-user text-blue-500 group-hover:scale-110 transition-transform"
                  ></i>
                  <span>My Account</span>
                </a>

                <a
                  href="{% url 'accounts:change_password' %}"
                  class="flex items-center gap-3 w-full text-gray-700 hover:text-[var(--primary-color)] px-3 py-2 rounded-xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-amber-50 transition-all duration-300 font-medium group"
                  title="Change Password"
                >
                  <i
                    class="fa-solid fa-key text-orange-500 group-hover:scale-110 transition-transform"
                  ></i>
                  <span>Change Password</span>
                </a>
              </div>

              <div class="border-t border-gray-100 px-2 py-2 w-full">
              <div class="px-2 py-1 space-y-1 w-full">
                {% if user|has_permission:"manage_system" or user.is_admin %}

                <a
                  href="{% url 'core:administration' %}"
                  class="flex items-center gap-3 w-full text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-[var(--primary-color)] px-3 py-2 rounded-xl transition-all duration-300 font-medium group"
                  title="Administration"
                >
                  <i
                    class="fa-solid fa-cog text-blue-500 group-hover:scale-110 transition-transform"
                  ></i>
                  <span>Administration</span>
                </a>
                <a
                  href="{% url 'academics:dashboard' %}"
                  class="flex items-center gap-3 w-full text-gray-700 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-[var(--primary-color)] px-3 py-2 rounded-xl transition-all duration-300 font-medium group"
                  title="Academics"
                >
                  <i
                    class="fa-solid fa-graduation-cap text-green-500 group-hover:scale-110 transition-transform"
                  ></i>
                  <span>Academics</span>
                </a>
                {% endif %}
                <div class="border-t border-gray-100 my-2"></div>
                <a
                  href="{% url 'accounts:logout' %}"
                  class="flex items-center gap-3 w-full text-gray-700 hover:text-red-600 px-3 py-2 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-300 font-medium group"
                  title="Logout"
                >
                  <i
                    class="fa-solid fa-sign-out-alt text-red-500 group-hover:scale-110 transition-transform"
                  ></i>
                  <span>Sign Out</span>
                </a>
              </div>
            </div>
          </div>
        </section>

        <!-- Mobile navigation menu -->
        <div
          id="mobile-menu"
          class="md:hidden absolute top-full left-0 w-full bg-white/95 backdrop-blur-md shadow-2xl border-t border-gray-200/50 hidden transition-all duration-300 ease-out z-50 rounded-b-2xl"
        >
          <div class="px-6 py-6 space-y-3">
            <a
              href="{% url 'students:home' %}"
              class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 font-semibold rounded-xl {% if request.resolver_match.url_name == 'home' %}nav-active bg-gradient-to-r from-blue-50 to-indigo-50{% endif %}"
            >
              <i class="fas fa-chart-line text-blue-500"></i>
              Dashboard
            </a>
            <a
              href="{% url 'students:classes' %}"
              class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-300 font-semibold rounded-xl {% if request.resolver_match.url_name == 'classes' %}nav-active bg-gradient-to-r from-green-50 to-emerald-50{% endif %}"
            >
              <i class="fas fa-users text-green-500"></i>
              Classes
            </a>
            <a
              href="{% url 'finances:receipts' %}"
              class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 transition-all duration-300 font-semibold rounded-xl {% if request.resolver_match.url_name == 'receipts' %}nav-active bg-gradient-to-r from-purple-50 to-violet-50{% endif %}"
            >
              <i class="fas fa-receipt text-purple-500"></i>
              Receipts
            </a>
            <a
              href="{% url 'finances:expenditures' %}"
              class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-orange-50 hover:to-amber-50 transition-all duration-300 font-semibold rounded-xl {% if request.resolver_match.url_name == 'expenditures' %}nav-active bg-gradient-to-r from-orange-50 to-amber-50{% endif %}"
            >
              <i class="fas fa-wallet text-orange-500"></i>
              Finances
            </a>

            {% if user|has_permission:"manage_system" or user.is_admin %}
            <a
              href="{% url 'accounts:user_management' %}"
              class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-cyan-50 hover:to-blue-50 transition-all duration-300 font-semibold rounded-xl {% if 'user_management' in request.resolver_match.url_name %}nav-active bg-gradient-to-r from-cyan-50 to-blue-50{% endif %}"
            >
              <i class="fas fa-users-cog text-cyan-500"></i>
              User Management
            </a>
            {% endif %}

            <!-- Mobile Reports dropdown -->
            <div class="space-y-2">
              <button
                id="mobile-reports-toggle"
                class="flex items-center justify-between w-full py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 font-semibold focus:outline-none rounded-xl"
              >
                <div class="flex items-center gap-3">
                  <i class="fas fa-chart-bar text-indigo-500"></i>
                  Reports
                </div>
                <svg
                  class="w-4 h-4 transition-transform duration-300"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2.5"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              <div id="mobile-reports-menu" class="hidden pl-6 space-y-2">
                <a
                  href="{% url 'finances:income_statement' %}"
                  class="flex items-center gap-3 py-2 px-4 text-sm text-gray-600 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 rounded-lg"
                >
                  <i class="fas fa-chart-line text-green-500 text-xs"></i>
                  Income Statement
                </a>
                <a
                  href="{% url 'finances:expenditure_statement' %}"
                  class="flex items-center gap-3 py-2 px-4 text-sm text-gray-600 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-300 rounded-lg"
                >
                  <i class="fas fa-chart-pie text-red-500 text-xs"></i>
                  Expenditure Statement
                </a>
                <a
                  href="{% url 'finances:fee_reports' %}"
                  class="flex items-center gap-3 py-2 px-4 text-sm text-gray-600 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 transition-all duration-300 rounded-lg"
                >
                  <i class="fas fa-money-check-alt text-purple-500 text-xs"></i>
                  Fee Payment Report
                </a>
              </div>
            </div>

            <!-- Mobile user menu -->
            <div class="border-t border-gray-200/50 pt-4 mt-4">
              <div
                class="flex items-center gap-4 mb-4 p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl"
              >
                <div class="relative">
                  <img
                    src="{% static 'img/person.png' %}"
                    alt="Profile"
                    class="rounded-full bg-gradient-to-br from-gray-200 to-gray-300 border-2 border-white shadow-lg"
                    width="36"
                    height="36"
                  />
                  <div
                    class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white"
                  ></div>
                </div>
                <div>
                  <span class="text-sm font-semibold capitalize text-gray-800"
                    >{{ request.user }}</span
                  >
                  <p class="text-xs text-gray-500">
                    {% if request.user.is_superuser %}
                    <span>Super Administrator</span>
                    {% else %}
                    <span>Administrator</span>
                    {% endif %}
                  </p>
                </div>
              </div>
              <div class="space-y-2">
                {% if user|has_permission:"manage_system" or user.is_admin %}

                <a
                  href="{% url 'core:administration' %}"
                  class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 font-medium rounded-xl"
                >
                  <i class="fa-solid fa-cog text-blue-500"></i>
                  Administration
                </a>
                <a
                  href="{% url 'academics:dashboard' %}"
                  class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-[var(--primary-color)] hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 transition-all duration-300 font-medium rounded-xl"
                >
                  <i class="fa-solid fa-graduation-cap text-green-500"></i>
                  Academics
                </a>
                {% endif %}
                <div class="border-t border-gray-200/50 my-3"></div>
                <a
                  href="{% url 'accounts:logout' %}"
                  class="flex items-center gap-3 py-3 px-4 text-gray-700 hover:text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-300 font-medium rounded-xl"
                >
                  <i class="fa-solid fa-sign-out-alt text-red-500"></i>
                  Sign Out
                </a>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- Main Content Area -->
    <main
      class="flex flex-col justify-center gap-8 items-center px-4 md:px-8 py-8 w-full min-h-[calc(100vh-80px)] relative"
    >
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-5 pointer-events-none">
        <div
          class="absolute inset-0"
          style="
            background-image: radial-gradient(
              circle at 1px 1px,
              rgba(122, 178, 211, 0.3) 1px,
              transparent 0
            );
            background-size: 20px 20px;
          "
        ></div>
      </div>

      <!-- Content -->
      <div class="relative z-10 w-full max-w-7xl mx-auto">
        {% block content %} {% endblock %}
      </div>
    </main>

    <!-- Footer -->
    <footer
      class="w-full bg-white/80 backdrop-blur-md border-t border-gray-200/50 py-6 mt-auto"
    >
      <div class="max-w-7xl mx-auto px-6">
        <div
          class="flex flex-col md:flex-row justify-between items-center gap-4"
        >
          <div class="flex items-center gap-2">
            <img
              src="{% static 'img/tinyfeet.jpg' %}"
              alt="Tiny Feet Academy"
              width="24"
              height="24"
              class="rounded-lg"
            />
            <span class="text-sm text-gray-600 font-medium">
              © 2024 Tiny Feet Academy. All rights reserved.
            </span>
          </div>
          <div class="flex items-center gap-4 text-sm text-gray-500">
            <span>Powered by</span>
            <span class="font-semibold text-[var(--primary-color)]"
              >Mahara RND</span
            >
          </div>
        </div>
      </div>
    </footer>

    <!-- Modern Interactions JavaScript -->
    <script src="{% static 'js/modern-interactions.js' %}"></script>

    {% block scripts %} {% endblock %}

    <!-- Mobile menu toggle function -->
    <script>
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobile-menu");
        if (mobileMenu) {
          const isHidden = mobileMenu.classList.contains("hidden");
          if (isHidden) {
            mobileMenu.classList.remove("hidden");
          } else {
            mobileMenu.classList.add("hidden");
          }
        }
      }

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (event) {
        const mobileMenu = document.getElementById("mobile-menu");
        const mobileMenuButton = document.getElementById("mobile-menu-button");

        if (mobileMenu && mobileMenuButton) {
          const isClickInsideMenu = mobileMenu.contains(event.target);
          const isClickOnButton = mobileMenuButton.contains(event.target);

          if (
            !isClickInsideMenu &&
            !isClickOnButton &&
            !mobileMenu.classList.contains("hidden")
          ) {
            mobileMenu.classList.add("hidden");
          }
        }
      });

      // Close mobile menu on window resize to desktop size
      window.addEventListener("resize", function () {
        const mobileMenu = document.getElementById("mobile-menu");
        if (window.innerWidth >= 768 && mobileMenu) {
          mobileMenu.classList.add("hidden");
        }
      });

      // Mobile dropdown toggles
      document.addEventListener("DOMContentLoaded", function () {
        const dropdownToggles = [
          { button: "mobile-reports-toggle", menu: "mobile-reports-menu" },
          { button: "mobile-teachers-toggle", menu: "mobile-teachers-menu" },
          {
            button: "mobile-performance-toggle",
            menu: "mobile-performance-menu",
          },
        ];

        dropdownToggles.forEach(({ button, menu }) => {
          const toggleButton = document.getElementById(button);
          const dropdownMenu = document.getElementById(menu);

          if (toggleButton && dropdownMenu) {
            toggleButton.addEventListener("click", function () {
              const isOpen = !dropdownMenu.classList.contains("hidden");
              const arrow = toggleButton.querySelector("svg");

              if (isOpen) {
                dropdownMenu.classList.add("hidden");
                if (arrow) arrow.style.transform = "rotate(0deg)";
              } else {
                dropdownMenu.classList.remove("hidden");
                if (arrow) arrow.style.transform = "rotate(180deg)";
              }
            });
          }
        });
      });
    </script>

    <!-- <script src="//unpkg.com/alpinejs" defer></script> -->
    <script src="{% static 'js/alpine.js' %}"></script>
    <script src="{% static 'js/main.js' %}"></script>
  </body>
</html>
