{% extends 'base.html' %} {% load static %} {% load humanize %}
<!--  -->
{% block title %}<PERSON><PERSON> List | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header -->
  <div class="card-modern p-8 breadcrumb-animation">
    <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
      <div class="flex items-center gap-4">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-2xl flex items-center justify-center shadow-lg icon-float"
        >
          <i class="fas fa-hand-holding-usd text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl text-[#2C3E50] title-slide-in"
          >
            <PERSON><PERSON>
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Manage and view all fee waivers
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#F28C8C] to-[#e07575] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'finances:receipts' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-receipt group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>View Receipts</span>
        </a>
      </div>
    </div>
    <nav class="flex items-center gap-3 text-sm font-medium breadcrumb-fade-in mt-6">
      <a href="{% url 'students:home' %}" class="text-[#40657F] hover:text-[#2C3E50] transition-colors">Home</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <a href="{% url 'finances:receipts' %}" class="text-[#40657F] hover:text-[#2C3E50] transition-colors">Finances</a>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#F28C8C] font-semibold">Waivers</span>
    </nav>
  </div>

  <!-- Search and Filters -->
  <div class="card-modern p-6 search-fade-in">
    <form method="get" class="flex flex-col md:flex-row gap-4">
      <div class="flex-1">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <i class="fas fa-search text-[#7AB2D3] text-lg"></i>
          </div>
          <input
            type="text"
            name="search"
            value="{{ search_query }}"
            placeholder="Search by waiver ID, student name, student ID, category, waived by, or reason..."
            class="w-full pl-12 pr-4 py-4 border-2 border-[#B9D8EB] rounded-xl focus:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/20 transition-all duration-300 text-[#2C3E50] placeholder-[#7AB2D3]/60 font-medium"
          />
        </div>
      </div>
      <button
        type="submit"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-4 px-8 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
      >
        <i class="fas fa-search"></i>
        <span>Search</span>
      </button>
      {% if search_query %}
      <a
        href="{% url 'finances:waiver_list' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
      >
        <i class="fas fa-times"></i>
        <span>Clear</span>
      </a>
      {% endif %}
    </form>
  </div>

  <!-- Results Summary -->
  {% if search_query %}
  <div class="card-modern p-6 results-fade-in">
    <div class="flex items-center gap-3">
      <div
        class="w-10 h-10 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center"
      >
        <i class="fas fa-info-circle text-white text-sm"></i>
      </div>
      <div>
        <p class="text-[#2C3E50] font-bold">
          Found {{ total_count }} waiver{{ total_count|pluralize }} for "{{ search_query }}"
        </p>
        <p class="text-[#40657F] text-sm">
          Showing results {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ total_count }}
        </p>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Waivers List -->
  <div class="card-modern overflow-hidden table-fade-in">
    {% if waivers %}
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gradient-to-r from-[#40657F] to-[#2C3E50] text-white">
          <tr>
            <th class="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-id-card mr-2"></i>Waiver ID
            </th>
            <th class="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-user mr-2"></i>Student
            </th>
            <th class="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-tag mr-2"></i>Category
            </th>
            <th class="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-money-bill mr-2"></i>Amount
            </th>
            <th class="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-calendar mr-2"></i>Date
            </th>
            <th class="px-6 py-4 text-left text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-user-tie mr-2"></i>Waived By
            </th>
            <th class="px-6 py-4 text-center text-sm font-bold uppercase tracking-wider">
              <i class="fas fa-cogs mr-2"></i>Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-[#B9D8EB]/30">
          {% for waiver in waivers %}
          <tr class="hover:bg-[#E2F1F9]/50 transition-colors duration-200 table-row-fade-in">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center gap-3">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-[#F28C8C] to-[#e07575] rounded-xl flex items-center justify-center"
                >
                  <i class="fas fa-receipt text-white text-sm"></i>
                </div>
                <div>
                  <p class="text-[#2C3E50] font-bold">{{ waiver.waiver_id }}</p>
                  <p class="text-[#40657F] text-sm">{{ waiver.account.term }}</p>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div>
                <p class="text-[#2C3E50] font-bold">{{ waiver.account.student.name }}</p>
                <p class="text-[#40657F] text-sm">{{ waiver.account.student.student_id }} • {{ waiver.account.student.level.level_name }}</p>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#7AB2D3]/10 text-[#40657F] border border-[#7AB2D3]/20">
                {{ waiver.account.category.name }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <p class="text-[#F28C8C] font-bold text-lg">K {{ waiver.amount_waived|intcomma }}</p>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <p class="text-[#2C3E50] font-medium">{{ waiver.date_waived|date:"M d, Y" }}</p>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <p class="text-[#40657F] font-medium">{{ waiver.waived_by }}</p>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-center">
              <div class="flex items-center justify-center gap-2">
                <a
                  href="{% url 'finances:view_waiver' waiver.waiver_id %}"
                  class="inline-flex items-center gap-2 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-2 px-4 rounded-lg hover:from-[#5fb085] hover:to-[#74C69D] transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg"
                  title="View Details"
                >
                  <i class="fas fa-eye text-sm"></i>
                  <span class="hidden sm:inline">View</span>
                </a>
                <a
                  href="{% url 'students:student_details' waiver.account.student.student_id %}"
                  class="inline-flex items-center gap-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-2 px-4 rounded-lg hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg"
                  title="View Student"
                >
                  <i class="fas fa-user text-sm"></i>
                  <span class="hidden sm:inline">Student</span>
                </a>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
    {% else %}
    <div class="text-center py-16">
      <div
        class="w-24 h-24 bg-gradient-to-br from-[#B9D8EB] to-[#E2F1F9] rounded-full flex items-center justify-center mx-auto mb-6"
      >
        <i class="fas fa-hand-holding-usd text-[#7AB2D3] text-3xl"></i>
      </div>
      <h3 class="text-2xl font-bold text-[#2C3E50] font-display mb-2">
        {% if search_query %}
        No Waivers Found
        {% else %}
        No Waivers Available
        {% endif %}
      </h3>
      <p class="text-[#40657F] mb-6">
        {% if search_query %}
        No waivers match your search criteria. Try adjusting your search terms.
        {% else %}
        There are currently no fee waivers in the system.
        {% endif %}
      </p>
      {% if search_query %}
      <a
        href="{% url 'finances:waiver_list' %}"
        class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-3 px-6 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
      >
        <i class="fas fa-list"></i>
        <span>View All Waivers</span>
      </a>
      {% endif %}
    </div>
    {% endif %}
  </div>

  <!-- Pagination -->
  {% if page_obj.has_other_pages %}
  <div class="card-modern p-6 pagination-fade-in">
    <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
      <div class="text-[#40657F] text-sm">
        Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} waivers
      </div>
      
      <div class="flex items-center gap-2">
        {% if page_obj.has_previous %}
        <a
          href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-2 px-4 rounded-lg hover:from-[#E2F1F9] hover:to-[#B9D8EB] border border-[#B9D8EB] hover:border-[#7AB2D3] transition-all duration-300"
        >
          <i class="fas fa-angle-double-left"></i>
          <span class="hidden sm:inline">First</span>
        </a>
        <a
          href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-2 px-4 rounded-lg hover:from-[#E2F1F9] hover:to-[#B9D8EB] border border-[#B9D8EB] hover:border-[#7AB2D3] transition-all duration-300"
        >
          <i class="fas fa-angle-left"></i>
          <span class="hidden sm:inline">Previous</span>
        </a>
        {% endif %}

        <span class="inline-flex items-center gap-2 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-2 px-4 rounded-lg">
          Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>

        {% if page_obj.has_next %}
        <a
          href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-2 px-4 rounded-lg hover:from-[#E2F1F9] hover:to-[#B9D8EB] border border-[#B9D8EB] hover:border-[#7AB2D3] transition-all duration-300"
        >
          <span class="hidden sm:inline">Next</span>
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}"
          class="inline-flex items-center gap-2 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-2 px-4 rounded-lg hover:from-[#E2F1F9] hover:to-[#B9D8EB] border border-[#B9D8EB] hover:border-[#7AB2D3] transition-all duration-300"
        >
          <span class="hidden sm:inline">Last</span>
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
  </div>
  {% endif %}
</section>

<style>
  /* Animations */
  .breadcrumb-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: breadcrumbSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(50px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  .search-fade-in {
    opacity: 0;
    animation: searchFadeIn 0.8s ease-out 1s forwards;
  }

  .results-fade-in {
    opacity: 0;
    animation: resultsFadeIn 0.8s ease-out 1.2s forwards;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .table-row-fade-in {
    opacity: 0;
    animation: tableRowFadeIn 0.6s ease-out forwards;
  }

  .table-row-fade-in:nth-child(odd) {
    animation-delay: 0.1s;
  }

  .table-row-fade-in:nth-child(even) {
    animation-delay: 0.2s;
  }

  .pagination-fade-in {
    opacity: 0;
    animation: paginationFadeIn 0.8s ease-out 1.6s forwards;
  }

  /* Keyframes */
  @keyframes breadcrumbSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 96px;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes searchFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes resultsFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes tableFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes tableRowFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes paginationFadeIn {
    to {
      opacity: 1;
    }
  }
</style>
{% endblock %}
