from datetime import date
import logging

from finances.book_keeping import JournalEntry, JournalLine, Ledger
from finances.fee_management.models import FeeAccount, Receipt
from django.db import transaction
from django.db.models import Sum

from students.models import Student
from dateutil.relativedelta import relativedelta


# Configer logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the current term and month
current_month = date.today().replace(day=1)
previous_month = (current_month - relativedelta(months=1)).replace(day=1)


def create_new_fee_accounts():
    logger.info("Running create_new_fee_accounts() function")
    students = Student.objects.filter(is_active=True)

    for student in students:
        student.save()
    logger.info("Completed create_new_fee_account() function")


def re_calculate_accounts():
    logger.info("Running re_calculate_accounts function")
    fee_accounts = FeeAccount.objects.filter(term__is_active=True)

    for account in fee_accounts:
        receipts = Receipt.objects.filter(
            fee_account=account, is_reversed=False)
        if not receipts:
            account.amount_paid = 0
            account.save()
            continue

        amount_paid = sum(receipt.amount_paid for receipt in receipts)
        try:
            with transaction.atomic():
                new_account = get_new_account(account, receipts.first())

                account.amount_paid = 0
                account.save()

                new_account.amount_paid = amount_paid
                new_account.save()

                logger.info(
                    f"Recalculated account for student {new_account.student} for month {new_account.month or "Termly"}")

        except FeeAccount.DoesNotExist:
            logger.error(
                f"FeeAccount does not exist for student {new_account.student} and month {new_account.month or 'Termly'}")
        except Exception as e:
            logger.error(
                f"Error recalculating account for student {account.student}: {e}")

    logger.info("Completed re_calculate_accounts function")


def get_student_account(student, month):
    """
    Helper function to get student's fee account for a student for a specific given month.
    """

    try:
        return FeeAccount.objects.get(student=student, month__month=month.month, term__is_active=True)
    except FeeAccount.DoesNotExist:
        logger.warning(
            f"Fee account for student {student} and month {month} does not exist.")
        return None


def get_new_account(account, receipt):
    if account.month:
        return FeeAccount.objects.get(
            term__is_active=True, month=receipt.date.replace(day=1), student=account.student
        )
    else:
        return account


def rearrange_receipts_by_month():
    '''
    In cases where the receipts had a different month to the fee_account
    this function will get those receipts and re_arrange them accordingly
    to reflect the date and actual fee account for that date(month)
    '''
    logger.info("Running rearrange_receipts_by_month function")
    receipts = Receipt.objects.filter(
        fee_account__term__is_active=True, is_reversed=False)

    for receipt in receipts:
        try:
            if receipt.fee_account.month is None:

                logger.info(
                    f"Skipping receipt {receipt.receipt_number} as fee account month is None")
                continue

            if receipt.date.month == receipt.fee_account.month.month:

                logger.info(
                    f"Skipping receipt {receipt.receipt_number} as it is already in the correct month")
                continue

            with transaction.atomic():
                # Get the correct fee account baseed on the receipt date
                correct_month = receipt.date.replace(day=1)
                correct_fee_account = FeeAccount.objects.get(
                    student=receipt.fee_account.student,
                    month=correct_month,
                    term__is_active=True,
                )

                receipt.fee_account = correct_fee_account
                receipt.save()

                logger.info(
                    f"Reassigned receipt {receipt.receipt_number} to correct month {correct_month}")

        except FeeAccount.DoesNotExist:
            logger.error(
                f"FeeAccount does not exist for student {receipt.fee_account.student} and month {correct_month}")
        except Exception as e:
            logger.error(
                f"Error reassigning receipt {receipt.receipt_number}: {e}")

    logger.info("Completed rearrange_receipts_by_month function")


def allign_journals():
    """
    Syncs journals with receipts using double-entry accounting.
    - Deletes journals without matching receipts.
    - Creates missing journals from receipts.
    - Updates mismatched amounts.
    """
    logger.info("Starting allign_journals function")

    receipts = Receipt.objects.filter(
        fee_account__term__is_active=True, is_reversed=False).select_related('fee_account__term')
    receipt_map = {r.receipt_number: r for r in receipts}

    journals = JournalLine.objects.filter(
        journal_entry__term__is_active=True,
        account__ledger_type="Revenue",
        line_type="Credit"
    ).select_related('journal_entry', 'account')

    journal_map = {j.journal_entry.voucher: j for j in journals}

    # STEP 1: Delete orphaned journals
    for voucher, journal in journal_map.items():
        if voucher not in receipt_map:
            journal.journal_entry.delete()  # Deletes both header and lines
            logger.info(
                f"Deleted orphaned journal {voucher} (no matching receipt)")

    # STEP 2: Create or update journals for receipts
    for receipt in receipts:
        voucher = receipt.receipt_number
        journal = journal_map.get(voucher)

        if journal:
            # Update if amount is off
            if journal.amount != receipt.amount_paid:
                journal.amount = receipt.amount_paid
                journal.save()
                logger.info(
                    f"Updated journal {voucher} amount to match receipt")
            continue

        revenue_account = Ledger.objects.get(
            name=receipt.fee_account.category.name)
        cash_account = Ledger.objects.get(name="Cash/Bank")

        entry = JournalEntry.objects.create(
            voucher=voucher,
            description=f"Receipt {voucher}",
            term=receipt.fee_account.term
        )

        JournalLine.objects.bulk_create([
            JournalLine(
                journal_entry=entry,
                account=cash_account,
                line_type="Debit",
                amount=receipt.amount_paid
            ),
            JournalLine(
                journal_entry=entry,
                account=revenue_account,
                line_type="Credit",
                amount=receipt.amount_paid
            )
        ])

        logger.info(f"Created journal {voucher} from receipt")

    # STEP 3: Verify total alignment
    total_receipts = receipts.aggregate(total=Sum("amount_paid"))['total'] or 0
    total_journals = JournalLine.objects.filter(
        journal_entry__term__is_active=True,
        account__ledger_type="Revenue",
        line_type="Credit"
    ).aggregate(total=Sum("amount"))['total'] or 0

    logger.info(f"Total receipt amount: {total_receipts}")
    logger.info(f"Total credited revenue: {total_journals}")

    if total_receipts != total_journals:
        logger.warning("Mismatch between receipts and revenue journals!")
    else:
        logger.info("Receipts and journals are perfectly aligned.")

    logger.info("Completed allign_journals function")
