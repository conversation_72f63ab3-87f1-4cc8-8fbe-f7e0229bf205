{% extends 'base.html' %}
{% load static %}
{% load rbac_tags %}
<!-- title -->
{% block title %}System Administration | {% endblock %}
<!-- title -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-cogs text-white text-lg sm:text-xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            System Administration
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Manage system-wide operations and configurations
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'students:home' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-home mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Dashboard
        </a>
        <a
          href="{% url 'students:students' %}"
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-users mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Students
        </a>
      </div>
    </div>

    <!-- Breadcrumb -->
    <nav
      class="flex items-center gap-2 text-sm font-medium mt-6 pt-6 border-t border-gray-200 breadcrumb-fade-in"
    >
      <span class="text-[#40657F]">Home</span>
      <i class="fa-solid fa-chevron-right text-[#B9D8EB] text-xs"></i>
      <span class="text-[#7AB2D3] font-semibold">Administration</span>
    </nav>
  </div>

  <!-- Warning Section -->
  <div
    class="bg-gradient-to-r from-[#FEF2F2] to-[#F28C8C]/10 border-2 border-[#F28C8C]/30 rounded-2xl p-8 flex items-center space-x-6 shadow-lg backdrop-blur-sm warning-section-fade-in"
  >
    <div
      class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e67373] rounded-full flex items-center justify-center shadow-lg animate-pulse"
    >
      <i class="fas fa-exclamation-triangle text-white text-lg"></i>
    </div>
    <div class="flex-1">
      <div class="flex items-center gap-2 mb-2">
        <i class="fas fa-shield-alt text-[#F28C8C]"></i>
        <p class="text-[#F28C8C] font-bold text-xl">Critical Operations Zone</p>
      </div>
      <p class="text-[#40657F] text-base font-medium leading-relaxed">
        These administrative functions affect multiple records across the
        system. Please proceed with extreme caution as these actions cannot be
        easily undone.
      </p>
      <div class="flex items-center gap-2 mt-3">
        <i class="fas fa-info-circle text-[#7AB2D3] text-sm"></i>
        <p class="text-[#7AB2D3] text-sm font-medium">
          It's recommended to backup your data before performing these
          operations.
        </p>
      </div>
    </div>
  </div>

  <!-- Administration Tools Grid -->
  <div class="admin-tools-grid-fade-in">
    <div class="flex items-center gap-4 mb-8">
      <div
        class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg"
      >
        <i class="fas fa-tools text-white text-lg"></i>
      </div>
      <div>
        <h2 class="text-2xl font-bold text-[#2C3E50] font-display">
          Administrative Tools
        </h2>
        <p class="text-[#40657F] text-sm">
          System maintenance and configuration utilities
        </p>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
    </div>

    <di class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Start New Term -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-calendar-plus text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#74C69D] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#74C69D] bg-[#74C69D]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Critical
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#74C69D] transition-colors duration-300"
          >
            Start New Term
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Creates new fee accounts for the upcoming term and initializes the
            academic period with proper configurations.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Creates fee accounts</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Initializes academic period</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Sets up term configurations</span
              >
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'core:start_new_term' %}"
          >
            <i
              class="fas fa-play mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Execute Operation
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#5fb085]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Academic Year Management -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-calendar-alt text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#7AB2D3] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#7AB2D3] bg-[#7AB2D3]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Essential
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
          >
            Academic Year Management
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Manage academic years, terms, and academic periods. Create new
            academic years, add terms, and control academic calendar.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Create academic years</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Add and manage terms</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Activate/close periods</span>
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'students:academic_year_management' %}"
          >
            <i
              class="fas fa-calendar-alt mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Manage Academic Years
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Budget & Ledger Management -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#74C69D]/5 to-[#5fb085]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#74C69D]/10 to-[#5fb085]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-chart-line text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#74C69D] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#74C69D] bg-[#74C69D]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Financial
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#74C69D] transition-colors duration-300"
          >
            Budget & Ledger Management
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Manage chart of accounts, create budgets, and control financial
            planning. Add ledger accounts and set up budget allocations.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Create ledger accounts</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Manage chart of accounts</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Create and manage budgets</span>
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'finances:budget_ledger_management' %}"
          >
            <i
              class="fas fa-chart-line mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Manage Budgets & Ledgers
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#5fb085]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Create Accounts -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-user-plus text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#7AB2D3] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#7AB2D3] bg-[#7AB2D3]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Standard
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
          >
            Create Accounts
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Creates new fee accounts for students that were added after the
            initial system setup process.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Creates missing accounts</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Updates student records</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Syncs fee structures</span>
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'core:run_create_student_accounts'%}"
          >
            <i
              class="fas fa-plus mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Execute Operation
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Re Calculate Accounts -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#F28C8C]/5 to-[#e67373]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#F28C8C]/10 to-[#e67373]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e67373] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-calculator text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#F28C8C] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#F28C8C] bg-[#F28C8C]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Critical
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#F28C8C] transition-colors duration-300"
          >
            Re Calculate Accounts
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Performs re-calculations to resolve inconsistencies in fee accounts
            and balances across the system.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#F28C8C] text-xs"></i>
              <span class="text-xs text-[#40657F]">Recalculates balances</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#F28C8C] text-xs"></i>
              <span class="text-xs text-[#40657F]">Fixes inconsistencies</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#F28C8C] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Updates account records</span
              >
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#F28C8C] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#e67373] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'core:run_recalculate_accounts'%}"
          >
            <i
              class="fas fa-sync mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Execute Operation
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#F28C8C]/5 to-[#e67373]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Re-align Revenue Journals -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#40657F]/5 to-[#2C3E50]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#40657F]/10 to-[#2C3E50]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-align-left text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#40657F] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#40657F] bg-[#40657F]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Standard
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#40657F] transition-colors duration-300"
          >
            Re-align Revenue Journals
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Re-aligns revenue journals to resolve inconsistencies in financial
            reports and accounting records.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#40657F] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Aligns journal entries with receipts</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#40657F] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Fixes report inconsistencies</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#40657F] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Updates revenue records</span
              >
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'core:allign_receipts_journals' %}"
          >
            <i
              class="fas fa-align-center mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Execute Operation
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#40657F]/5 to-[#2C3E50]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Rearrange Receipts -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#B9D8EB]/5 to-[#7AB2D3]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#B9D8EB]/10 to-[#7AB2D3]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#B9D8EB] to-[#7AB2D3] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-receipt text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#7AB2D3] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#7AB2D3] bg-[#7AB2D3]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Standard
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
          >
            Rearrange Receipts
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Ensures each receipt is properly assigned to the correct fee account
            for the month the payment was made.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Assigns receipts correctly</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Organizes by payment date</span
              >
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Updates fee accounts</span>
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'core:rearrange_receipts' %}"
          >
            <i
              class="fas fa-sort mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Execute Operation
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#B9D8EB]/5 to-[#7AB2D3]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Academic Management -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#7AB2D3] to-[#40657F] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-graduation-cap text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#7AB2D3] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#7AB2D3] bg-[#7AB2D3]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Academic
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#7AB2D3] transition-colors duration-300"
          >
            Academic Management
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Comprehensive management of subjects, assessment types, activity types, and academic activities. Configure your curriculum and organize academic operations.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Manage subjects and curriculum</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]">Configure assessment types</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#7AB2D3] text-xs"></i>
              <span class="text-xs text-[#40657F]"
                >Organize activity types & activities</span
              >
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#7AB2D3] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#40657F] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'academics:academic_management_dashboard' %}"
          >
            <i
              class="fas fa-cogs mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Access Management
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#7AB2D3]/5 to-[#40657F]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- System Discrepancy Check -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#F28C8C]/5 to-[#e67373]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#F28C8C]/10 to-[#e67373]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#F28C8C] to-[#e67373] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-search text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#F28C8C] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#F28C8C] bg-[#F28C8C]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Diagnostic
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#F28C8C] transition-colors duration-300"
          >
            System Discrepancy Check
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Analyzes financial data integrity and identifies inconsistencies between ledgers, receipts, and account calculations.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#F28C8C] text-xs"></i>
              <span class="text-xs text-[#40657F]">Validates ledger calculations</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#F28C8C] text-xs"></i>
              <span class="text-xs text-[#40657F]">Identifies data inconsistencies</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#F28C8C] text-xs"></i>
              <span class="text-xs text-[#40657F]">Provides detailed analysis</span>
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#F28C8C] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#e67373] focus:ring-4 focus:ring-[#F28C8C]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'core:discrepancy_check' %}"
          >
            <i
              class="fas fa-search mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Run Diagnostic
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#F28C8C]/5 to-[#e67373]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>

      <!-- Class Progression -->
      <div
        class="admin-tool-card group bg-white rounded-2xl shadow-lg border border-[#B9D8EB]/30 p-8 hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 overflow-hidden relative bg-gradient-to-br from-[#7AB2D3]/5 to-[#40657F]/5"
      >
        <!-- Background Pattern -->
        <div
          class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-[#7AB2D3]/10 to-[#40657F]/10 rounded-full -translate-y-10 translate-x-10 transition-all duration-500 group-hover:scale-125 group-hover:rotate-45"
        ></div>

        <!-- Tool Header -->
        <div class="flex items-center justify-between mb-6 relative z-10">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300"
          >
            <i class="fas fa-search text-white text-lg"></i>
          </div>
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 bg-[#74C69D] rounded-full animate-pulse"></div>
            <span
              class="text-xs font-bold text-[#74C69D] bg-[#74C69D]/10 px-2 py-1 rounded-full uppercase tracking-wider"
            >
              Critical
            </span>
          </div>
        </div>

        <!-- Tool Content -->
        <div class="relative z-10">
          <h3
            class="text-xl font-bold text-[#2C3E50] mb-3 font-display group-hover:text-[#74C69D] transition-colors duration-300"
          >
            System Discrepancy Check
          </h3>
          <p class="text-[#40657F] text-sm mb-6 leading-relaxed">
            Analyzes financial data integrity and identifies inconsistencies between ledgers, receipts, and account calculations.
          </p>

          <!-- Features List -->
          <div class="space-y-2 mb-6">
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Validates ledger calculations</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Identifies data inconsistencies</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fas fa-check text-[#74C69D] text-xs"></i>
              <span class="text-xs text-[#40657F]">Provides detailed analysis</span>
            </div>
          </div>

          <!-- Action Button -->
          <a
            class="bg-[#74C69D] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#5fb085] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl btn-modern group/btn text-sm inline-flex items-center w-full justify-center"
            href="{% url 'students:student_progression' %}"
          >
            <i
              class="fas fa-bars-progress mr-2 group-hover/btn:scale-110 transition-transform duration-300"
            ></i>
            Progress Students
          </a>
        </div>

        <!-- Hover Glow Effect -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-[#74C69D]/5 to-[#5fb085]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        ></div>
      </div>
      
      
    </div>
    
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  .breadcrumb-fade-in {
    opacity: 0;
    animation: breadcrumbFadeIn 0.8s ease-out 0.4s forwards;
  }

  /* Warning Section Animation */
  .warning-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: warningSectionFadeIn 0.8s ease-out 1s forwards;
  }

  /* Admin Tools Grid Animations */
  .admin-tools-grid-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: adminToolsGridFadeIn 0.8s ease-out 1.2s forwards;
  }

  .admin-tool-card {
    opacity: 0;
    transform: translateY(30px);
    animation: adminToolCardSlideIn 0.6s ease-out forwards;
  }

  .admin-tool-card:nth-child(1) {
    animation-delay: 1.4s;
  }
  .admin-tool-card:nth-child(2) {
    animation-delay: 1.5s;
  }
  .admin-tool-card:nth-child(3) {
    animation-delay: 1.6s;
  }
  .admin-tool-card:nth-child(4) {
    animation-delay: 1.7s;
  }
  .admin-tool-card:nth-child(5) {
    animation-delay: 1.8s;
  }
  .admin-tool-card:nth-child(6) {
    animation-delay: 1.9s;
  }
  .admin-tool-card:nth-child(7) {
    animation-delay: 2.0s;
  }
  .admin-tool-card:nth-child(8) {
    animation-delay: 2.1s;
  }
  .admin-tool-card:nth-child(9) {
    animation-delay: 2.2s;
  }
  .admin-tool-card:nth-child(10) {
    animation-delay: 2.3s;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes iconFloat {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  @keyframes iconPulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes titleSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes subtitleFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes accentLineGrow {
    to {
      width: 5rem;
    }
  }

  @keyframes actionButtonsSlideIn {
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes breadcrumbFadeIn {
    to {
      opacity: 1;
    }
  }

  @keyframes warningSectionFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes adminToolsGridFadeIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes adminToolCardSlideIn {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .admin-tool-card {
      animation-delay: 1.2s;
    }

    .admin-tool-card:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--card-index, 1));
    }
  }
</style>

{% endblock %}
