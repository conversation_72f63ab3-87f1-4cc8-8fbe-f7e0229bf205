from django.urls import path

from .views import administration, rearrange_receipts, run_create_student_accounts, run_recalculate_accounts, start_new_term, allign_receipts_journals
from .views.administration import discrepancy_check, get_fee_categories
from .views.philosophical_easter_egg import philosophical_easter_egg, get_new_quote
from .views.music_easter_egg import music_easter_egg, get_new_music_quote
from .views.space_easter_egg import space_easter_egg, get_new_space_quote
from .views.art_easter_egg import art_easter_egg, get_new_art_quote
from .views.travel_easter_egg import travel_easter_egg, get_new_travel_quote
from .views.dashboard_api import get_notifications, mark_notification_read, get_calendar_events, get_upcoming_events

app_name = "core"

urlpatterns = [
    path('create-accounts',
         run_create_student_accounts, name='run_create_student_accounts'),
    path('recalculate-accounts',
         run_recalculate_accounts, name='run_recalculate_accounts'),
    path('start-start-new-term',
         start_new_term, name="start_new_term"),
    path('rearrange-reciepts',
         rearrange_receipts, name='rearrange_receipts'),
    path('align-receipts-journals',
         allign_receipts_journals, name='allign_receipts_journals'),
    path('discrepancy-check/',
         discrepancy_check, name='discrepancy_check'),
    path('api/fee-categories/',
         get_fee_categories, name='get_fee_categories'),

    # Philosophical Easter Egg URLs
    path('philosophical-moment/', philosophical_easter_egg, name='philosophical_easter_egg'),
    path('api/new-quote/', get_new_quote, name='get_new_quote'),

    # Music Easter Egg URLs
    path('musical-moment/', music_easter_egg, name='music_easter_egg'),
    path('api/new-music-quote/', get_new_music_quote, name='get_new_music_quote'),

    # Space Easter Egg URLs
    path('cosmic-moment/', space_easter_egg, name='space_easter_egg'),
    path('api/new-space-quote/', get_new_space_quote, name='get_new_space_quote'),

    # Art Easter Egg URLs
    path('artistic-moment/', art_easter_egg, name='art_easter_egg'),
    path('api/new-art-quote/', get_new_art_quote, name='get_new_art_quote'),

    # Travel Easter Egg URLs
    path('wanderlust-moment/', travel_easter_egg, name='travel_easter_egg'),
    path('api/new-travel-quote/', get_new_travel_quote, name='get_new_travel_quote'),

    # Dashboard API URLs
    path('api/notifications/', get_notifications, name='get_notifications'),
    path('api/notifications/mark-read/', mark_notification_read, name='mark_notification_read'),
    path('api/calendar/events/', get_calendar_events, name='get_calendar_events'),
    path('api/calendar/upcoming/', get_upcoming_events, name='get_upcoming_events'),

    path("", administration, name="administration"),

]
