{% extends 'base.html' %} {% load humanize %}
<!--  -->
{% block title %}Receipt Search | {% endblock %}
<!--  -->
{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Header Section -->
  <div class="card-modern p-8 header-animation">
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <div class="flex items-center gap-6">
        <div
          class="w-16 h-16 bg-gradient-to-br from-[#74C69D] to-[#5fb085] rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i class="fas fa-search text-white text-2xl icon-pulse"></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Receipt Search
          </h1>
          <p class="text-[#40657F] text-lg font-medium subtitle-fade-in">
            Search and filter payment receipts
          </p>
          <div
            class="w-24 h-1 bg-gradient-to-r from-[#74C69D] to-[#5fb085] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 action-buttons-slide-in">
        <a
          href="{% url 'finances:with_excel' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white font-bold py-4 px-8 rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-plus group-hover:rotate-90 transition-all duration-300"
          ></i>
          <span>Add Payment</span>
        </a>
        <a
          href="{% url 'finances:receipts' %}"
          class="inline-flex items-center gap-3 bg-gradient-to-r from-[#B9D8EB] to-[#E2F1F9] text-[#40657F] font-bold py-4 px-8 rounded-xl hover:from-[#E2F1F9] hover:to-[#B9D8EB] border-2 border-[#B9D8EB] hover:border-[#7AB2D3] focus:ring-4 focus:ring-[#7AB2D3]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-2xl group"
        >
          <i
            class="fas fa-calendar group-hover:scale-110 group-hover:rotate-12 transition-all duration-300"
          ></i>
          <span>Monthly View</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Advanced Search Section -->
  <div class="card-modern p-6 search-fade-in">
    <form method="GET" class="space-y-6">
      <!-- Main Search -->
      <div class="flex flex-col lg:flex-row gap-4">
        <div class="flex-1">
          <div class="relative">
            <input
              type="text"
              name="search"
              value="{{ search_query }}"
              placeholder="Search by student name, ID, receipt number, category, or class..."
              class="w-full px-4 py-3 pl-12 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] placeholder-[#40657F]/60"
            />
            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-[#7AB2D3]"></i>
          </div>
        </div>
        <div class="flex gap-3">
          <button
            type="submit"
            class="px-6 py-3 bg-gradient-to-r from-[#7AB2D3] to-[#40657F] text-white rounded-xl hover:from-[#40657F] hover:to-[#7AB2D3] transition-all duration-200 font-semibold w-full md:w-fit"
          >
            <i class="fas fa-search mr-2"></i>
            Search
          </button>
          {% if search_query or date_from or date_to or category_filter or level_filter %}
          <a
            href="{% url 'finances:receipt_list' %}"
            class="px-6 py-3 bg-[#B9D8EB] text-[#40657F] rounded-xl hover:bg-[#E2F1F9] transition-all duration-200 font-semibold w-full md:w-fit"
          >
            <i class="fas fa-times mr-2"></i>
            Clear All
          </a>
          {% endif %}
        </div>
      </div>

      <!-- Advanced Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Date From</label>
          <input
            type="date"
            name="date_from"
            value="{{ date_from }}"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]"
          />
        </div>
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Date To</label>
          <input
            type="date"
            name="date_to"
            value="{{ date_to }}"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50]"
          />
        </div>
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Category</label>
          <select
            name="category"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white"
          >
            <option value="" class="text-[#2C3E50]">All Categories</option>
            {% for category in categories %}
            <option value="{{ category }}" class="text-[#2C3E50]" {% if category == category_filter %}selected{% endif %}>
              {{ category }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div>
          <label class="block text-sm font-semibold text-[#2C3E50] mb-2">Class Level</label>
          <select
            name="level"
            class="w-full px-4 py-3 border border-[#B9D8EB] rounded-xl focus:ring-2 focus:ring-[#7AB2D3] focus:border-transparent transition-all duration-200 text-[#2C3E50] bg-white"
          >
            <option value="" class="text-[#2C3E50]">All Levels</option>
            {% for level in levels %}
            <option value="{{ level }}" class="text-[#2C3E50]" {% if level == level_filter %}selected{% endif %}>
              {{ level }}
            </option>
            {% endfor %}
          </select>
        </div>
      </div>
    </form>
  </div>

  <!-- Results Section -->
  <div class="card-modern p-8 table-section-fade-in">
    <div class="flex flex-col md:flex-row items-center gap-4 mb-8">
      <div class="flex justify-center items-start gap-4">
        <div
          class="w-12 h-12 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-xl flex items-center justify-center shadow-lg table-icon-float"
        >
          <i class="fas fa-table text-white text-lg"></i>
        </div>
        <div>
          <h3 class="text-2xl font-bold text-[#2C3E50] font-display">
            Search Results
          </h3>
          <p class="text-[#40657F] text-sm">
            {% if search_query %}
              Results for "{{ search_query }}"
            {% else %}
              All receipt records
            {% endif %}
          </p>
        </div>
      </div>
      <div
        class="flex-1 h-px bg-gradient-to-r from-[#B9D8EB] to-transparent"
      ></div>
      <div
        class="flex items-center gap-2 bg-[#74C69D]/20 text-[#74C69D] px-4 py-2 rounded-full font-bold border border-[#74C69D]/30 w-full md:w-fit"
      >
        <i class="fas fa-receipt text-sm"></i>
        <span>{{ total_count }} record{{ total_count|pluralize }}</span>
      </div>
    </div>

    <div
      class="overflow-x-auto rounded-2xl border border-[#B9D8EB]/50 shadow-lg table-fade-in"
    >
      <table class="min-w-full bg-white">
        <thead class="bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB]">
          <tr>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-hashtag text-[#7AB2D3]"></i>
                Receipt No
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-user text-[#40657F]"></i>
                Student Name
              </div>
            </th>
            <th
              class="px-6 py-4 text-left text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center gap-2">
                <i class="fas fa-tag text-[#74C69D]"></i>
                Category
              </div>
            </th>
            <th
              class="px-6 py-4 text-right text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-money-bill text-[#74C69D]"></i>
                Amount
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-calendar text-[#F28C8C]"></i>
                Date
              </div>
            </th>
            <th
              class="px-6 py-4 text-center text-sm font-bold text-[#2C3E50] uppercase tracking-wider"
            >
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-graduation-cap text-[#7AB2D3]"></i>
                Class
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-[#B9D8EB]/30">
          {% for receipt in receipts %}
          <tr
            class="receipt-row hover:bg-[#E2F1F9]/50 transition-colors duration-200"
          >
            <td class="px-6 py-4">
              <a
                href="{% url 'finances:generate_pdf' receipt.student.student_id receipt.receipt_number %}"
                class="inline-flex items-center gap-2 text-[#7AB2D3] hover:text-[#40657F] font-bold hover:underline transition-colors duration-200"
              >
                <i class="fas fa-file-pdf text-[#F28C8C]"></i>
                {{ receipt.receipt_number }}
              </a>
            </td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div
                  class="w-10 h-10 bg-gradient-to-br from-[#40657F] to-[#2C3E50] rounded-full flex items-center justify-center text-white text-sm font-bold"
                >
                  {{ receipt.student.name|first|upper }}
                </div>
                <div>
                  <div class="font-bold text-[#2C3E50] capitalize">
                    {{ receipt.student.name }}
                  </div>
                  <div class="text-sm text-[#40657F]">
                    ID: {{ receipt.student.student_id }}
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#74C69D]/20 text-[#74C69D] border border-[#74C69D]/30 text-nowrap capitalize"
              >
                <i class="fas fa-tag mr-1"></i>
                {{ receipt.fee_account.category }}
              </span>
            </td>
            <td class="px-6 py-4 text-right">
              <div class="flex items-center justify-end gap-2">
                <i class="fas fa-coins text-[#74C69D]"></i>
                <span class="font-bold text-[#74C69D] text-xl font-display text-nowrap">
                  MWK {{ receipt.amount_paid|intcomma }}
                </span>
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <div class="flex items-center justify-center gap-2">
                <i class="fas fa-calendar-day text-[#F28C8C]"></i>
                <span class="font-medium text-[#2C3E50]"
                  >{{ receipt.date }}</span
                >
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-semibold bg-[#7AB2D3]/20 text-[#7AB2D3] border border-[#7AB2D3]/30"
              >
                <i class="fas fa-graduation-cap mr-1"></i>
                {{ receipt.student.level }}
              </span>
            </td>
          </tr>
          {% empty %}
          <tr>
            <td colspan="6" class="text-center py-12">
              <div class="flex flex-col items-center gap-4">
                <div
                  class="w-16 h-16 bg-[#B9D8EB]/30 rounded-full flex items-center justify-center"
                >
                  <i class="fas fa-search text-[#B9D8EB] text-2xl"></i>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-[#2C3E50] mb-2">
                    No Receipts Found
                  </h3>
                  <p class="text-[#40657F]">
                    {% if search_query %}
                      No receipts match your search criteria. Try adjusting your filters.
                    {% else %}
                      There are no payment receipts to display.
                    {% endif %}
                  </p>
                </div>
                <a
                  href="{% url 'finances:with_excel' %}"
                  class="inline-flex items-center gap-2 bg-gradient-to-r from-[#74C69D] to-[#5fb085] text-white font-bold py-3 px-6 rounded-xl hover:from-[#5fb085] hover:to-[#74C69D] focus:ring-4 focus:ring-[#74C69D]/30 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
                >
                  <i class="fas fa-plus"></i>
                  <span>Add First Payment</span>
                </a>
              </div>
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="flex justify-center items-center gap-4 mt-8 pagination-fade-in">
      <div class="flex items-center gap-2">
        {% if page_obj.has_previous %}
        <a
          href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-left"></i>
        </a>
        <a
          href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-left"></i>
        </a>
        {% endif %}

        <span class="px-4 py-2 bg-[#7AB2D3] text-white rounded-lg font-semibold">
          Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
        </span>

        {% if page_obj.has_next %}
        <a
          href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-right"></i>
        </a>
        <a
          href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if level_filter %}&level={{ level_filter }}{% endif %}"
          class="px-3 py-2 bg-[#B9D8EB] text-[#40657F] rounded-lg hover:bg-[#7AB2D3] hover:text-white transition-all duration-200"
        >
          <i class="fas fa-angle-double-right"></i>
        </a>
        {% endif %}
      </div>
    </div>
    {% endif %}
  </div>
</section>

<style>
  /* Header Animations */
  .header-animation {
    opacity: 0;
    transform: translateY(-30px);
    animation: headerSlideDown 0.8s ease-out forwards;
  }

  .icon-float {
    animation: iconFloat 3s ease-in-out infinite;
  }

  .icon-pulse {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .title-slide-in {
    opacity: 0;
    transform: translateX(-50px);
    animation: titleSlideIn 0.8s ease-out 0.2s forwards;
  }

  .subtitle-fade-in {
    opacity: 0;
    animation: subtitleFadeIn 0.8s ease-out 0.4s forwards;
  }

  .accent-line-grow {
    width: 0;
    animation: accentLineGrow 0.8s ease-out 0.6s forwards;
  }

  .action-buttons-slide-in {
    opacity: 0;
    transform: translateX(30px);
    animation: actionButtonsSlideIn 0.8s ease-out 0.8s forwards;
  }

  /* Search Section Animations */
  .search-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: searchFadeIn 0.8s ease-out 1s forwards;
  }

  /* Table Section Animations */
  .table-section-fade-in {
    opacity: 0;
    transform: translateY(30px);
    animation: tableSectionFadeIn 0.8s ease-out 1.2s forwards;
  }

  .table-icon-float {
    animation: tableIconFloat 4s ease-in-out infinite;
  }

  .table-fade-in {
    opacity: 0;
    animation: tableFadeIn 0.8s ease-out 1.4s forwards;
  }

  .receipt-row {
    opacity: 0;
    transform: translateX(-20px);
    animation: receiptRowSlideIn 0.4s ease-out forwards;
  }

  .receipt-row:nth-child(1) { animation-delay: 1.6s; }
  .receipt-row:nth-child(2) { animation-delay: 1.7s; }
  .receipt-row:nth-child(3) { animation-delay: 1.8s; }
  .receipt-row:nth-child(4) { animation-delay: 1.9s; }
  .receipt-row:nth-child(5) { animation-delay: 2s; }
  .receipt-row:nth-child(6) { animation-delay: 2.1s; }
  .receipt-row:nth-child(7) { animation-delay: 2.2s; }
  .receipt-row:nth-child(8) { animation-delay: 2.3s; }
  .receipt-row:nth-child(9) { animation-delay: 2.4s; }
  .receipt-row:nth-child(10) { animation-delay: 2.5s; }

  .pagination-fade-in {
    opacity: 0;
    animation: paginationFadeIn 0.8s ease-out 2.6s forwards;
  }

  /* Keyframe Definitions */
  @keyframes headerSlideDown {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
  }

  @keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes titleSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes subtitleFadeIn {
    to { opacity: 1; }
  }

  @keyframes accentLineGrow {
    to { width: 6rem; }
  }

  @keyframes actionButtonsSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes searchFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableSectionFadeIn {
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes tableIconFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-6px) rotate(-3deg); }
  }

  @keyframes tableFadeIn {
    to { opacity: 1; }
  }

  @keyframes receiptRowSlideIn {
    to { opacity: 1; transform: translateX(0); }
  }

  @keyframes paginationFadeIn {
    to { opacity: 1; }
  }

  /* Responsive Adjustments */
  @media (max-width: 768px) {
    .receipt-row {
      animation-delay: 1.2s;
    }

    .receipt-row:nth-child(n) {
      animation-delay: calc(1.2s + 0.1s * var(--row-index, 1));
    }
  }
</style>

{% endblock %}
