from datetime import datetime
from django.db import models

from helpers.strings import generate_unique_ids
from .fee_account import FeeAccount
from finances.fee_management.utils import sync_income


class FeesWaiver(models.Model):
    waiver_id = models.CharField(
        max_length=50, unique=True, blank=True, null=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)
    account = models.ForeignKey(FeeAccount, on_delete=models.CASCADE)
    amount_waived = models.IntegerField(default=0)
    waived_by = models.CharField(max_length=100)
    date_waived = models.DateField(default=datetime.today)
    reason = models.Char<PERSON>ield(max_length=255)

    def apply_waived_fees(self):
        self.account.total_due -= self.amount_waived
        self.account.save()
        sync_income(self.account, self.date_waived)

    def generate_waiver_id(self):
        prefix = f"EXE{self.account.student.level.abbrv}"
        self.waiver_id = generate_unique_ids(self, prefix)

    def save(self, *args, **kwargs):
        if not self.id:
            super().save(*args, **kwargs)
            self.apply_waived_fees()

        if not self.waiver_id:
            self.generate_waiver_id()
            super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.waiver_id} : {self.account.student.name} {self.account.category.name}"

    class Meta:
        unique_together = ('account', 'date_waived', 'amount_waived')
