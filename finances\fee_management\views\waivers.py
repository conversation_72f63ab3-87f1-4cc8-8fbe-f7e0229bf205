from django.shortcuts import redirect, render
from django.contrib.auth.decorators import login_required
from django.contrib import messages

from students.models import Student
from finances.fee_management.forms import WaiverForm


@login_required(login_url="accounts:login")
def waive_fees(request, slug):
    student = Student.objects.get(student_id=slug)

    form = WaiverForm()
    if request.method == 'POST':
        form = WaiverForm(request.POST)
        if form.is_valid():
            waive_fees = form.save(student=student)

            waive_fees.save()
            messages.success(
                request, f"{waive_fees.student} created successfully")

            return redirect('students:student_details', slug)

    context = {
        'form': form,
        'student': student,
    }

    return render(request, "receipts/waiver_form.html", context)
