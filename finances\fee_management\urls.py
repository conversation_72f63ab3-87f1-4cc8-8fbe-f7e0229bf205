from django.urls import path
from . import views

urlpatterns = [
    path('', views.receipt_payment_months, name="receipts"),
    path('search/', views.receipt_list, name="receipt_list"),
    path('<slug:slug>', views.receipts, name="receipt_months"),
    path('add-receipt/<slug:slug>', views.add_receipt, name='add_receipt'),
    path('waive-fees/<slug:slug>', views.waive_fees, name='waive_fees'),
    path('generate-pdf/<path:student_id>/<path:receipt_number>',
         views.generate_receipt_pdf, name='generate_pdf'),
    path('generate-excel/',
         views.GenerateExcelView.as_view(), name="generate_excel"),
    path('with-excel/',
         views.ExcelPaymentView.as_view(), name='with_excel'),
]
